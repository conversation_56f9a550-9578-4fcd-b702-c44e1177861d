<?php
defined('BASEPATH') OR exit('No direct script access allowed');

require_once(APPPATH . 'third_party/mpdf/autoload.php');

class Pdf {
    protected $mpdf;
    
    public function __construct() {
        $this->mpdf = new \Mpdf\Mpdf([
            'mode' => 'utf-8',
            'format' => 'A4',
            'margin_header' => '3',
            'margin_top' => '20',
            'margin_left' => '15',
            'margin_right' => '15',
            'margin_bottom' => '20',
            'margin_footer' => '3'
        ]);
    }

    public function load($html, $filename = 'document.pdf') {
        $this->mpdf->WriteHTML($html);
        $this->mpdf->Output($filename, 'D');
    }
}