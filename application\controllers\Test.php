<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Test extends CI_Controller
{
    public function index()
    {
        echo "<h1>Page de test</h1>";
        echo "<p>Si vous voyez cette page, le routage fonctionne correctement.</p>";
        
        echo "<h2>Informations de débogage:</h2>";
        echo "<p>Utilisateur connecté: " . (is_loggedin() ? 'Oui' : 'Non') . "</p>";
        
        if (is_loggedin()) {
            echo "<p>Rôle: " . loggedin_role_name() . " (ID: " . loggedin_role_id() . ")</p>";
            echo "<p>Branch ID: " . get_loggedin_branch_id() . "</p>";
            
            echo "<h3>Permissions:</h3>";
            echo "<p>student_dropout (view): " . (get_permission('student_dropout', 'is_view') ? 'Oui' : 'Non') . "</p>";
            echo "<p>leave_manage (view): " . (get_permission('leave_manage', 'is_view') ? 'Oui' : 'Non') . "</p>";
        }
        
        echo "<h3>Contrôleurs:</h3>";
        echo "<p>Dropout existe: " . (file_exists(APPPATH . 'controllers/Dropout.php') ? 'Oui' : 'Non') . "</p>";
        echo "<p>Leave existe: " . (file_exists(APPPATH . 'controllers/Leave.php') ? 'Oui' : 'Non') . "</p>";
        
        echo "<h3>Modèles:</h3>";
        echo "<p>dropout_model existe: " . (file_exists(APPPATH . 'models/Dropout_model.php') ? 'Oui' : 'Non') . "</p>";
        echo "<p>leave_model existe: " . (file_exists(APPPATH . 'models/Leave_model.php') ? 'Oui' : 'Non') . "</p>";
        
        echo "<h3>Vues:</h3>";
        echo "<p>dropout/index existe: " . (file_exists(APPPATH . 'views/dropout/index.php') ? 'Oui' : 'Non') . "</p>";
        echo "<p>leave/index existe: " . (file_exists(APPPATH . 'views/leave/index.php') ? 'Oui' : 'Non') . "</p>";
    }
}