<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Dropout extends Admin_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('dropout_model');
        $this->load->model('student_model');
        $this->load->model('email_model');
    }

    public function index()
    {
        if (!get_permission('student_dropout', 'is_view')) {
            access_denied();
        }
        
        if ($_POST) {
            if (!get_permission('student_dropout', 'is_add')) {
                access_denied();
            }
            if (is_superadmin_loggedin()) {
                $this->form_validation->set_rules('branch_id', translate('branch'), 'required');
            }
            $this->form_validation->set_rules('student_id', translate('student'), 'required');
            $this->form_validation->set_rules('dropout_date', translate('dropout_date'), 'required');
            $this->form_validation->set_rules('reason_id', translate('reason'), 'required');
            $this->form_validation->set_rules('comments', translate('comments'), 'trim');
            
            if ($this->form_validation->run() !== false) {
                $data = $this->input->post();
                $this->dropout_model->save($data);
                set_alert('success', translate('information_has_been_saved_successfully'));
                redirect(base_url('dropout'));
            }
        }
        
        $this->data['dropoutlist'] = $this->dropout_model->getDropoutList();
        $this->data['title'] = translate('student_dropout');
        $this->data['sub_page'] = 'dropout/index';
        $this->data['main_menu'] = 'student';
        $this->load->view('layout/index', $this->data);
    }

    public function delete($id = '')
    {
        if (get_permission('student_dropout', 'is_delete')) {
            if (!is_superadmin_loggedin()) {
                $this->db->where('branch_id', get_loggedin_branch_id());
            }
            $this->db->where('id', $id);
            $this->db->delete('student_dropout');
        }
    }
}