<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');
require_once APPPATH . '/third_party/mpdf/autoload.php';

class Html2pdf
{
    public $mpdf;
    public function __construct()
    {
        $this->mpdf = new \Mpdf\Mpdf([
            'default_font' => 'dejavusans',
            'mode' => 'utf-8',
            'margin_left' => 2,
            'margin_right' => 2,
            'margin_top' => 2,
            'margin_bottom' => 2,
            'format' => 'A4',
            'setAutoTopMargin' => 'stretch',
            'setAutoBottomMargin' => 'stretch',
            'defaultCssFile' => APPPATH . 'assets/css/pdf_styles.css'
        ]);
        
        // Définir les styles personnalisés
        $this->mpdf->WriteHTML('
            <style>
                .bg-blue { background-color: rgb(240, 247, 255) !important; }
                .bg-gray { background-color: rgb(249, 249, 249) !important; }
                .success-badge {
                    background-color: #28a745 !important;
                    color: white !important;
                    padding: 4px 8px !important;
                    font-weight: bold !important;
                }
                .danger-badge {
                    background-color: #dc3545 !important;
                    color: white !important;
                    padding: 4px 8px !important;
                    font-weight: bold !important;
                }
            </style>
        ');
    }
}
