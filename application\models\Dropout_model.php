<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Dropout_model extends MY_Model
{
    public function __construct()
    {
        parent::__construct();
    }

    public function getDropoutList()
    {
        $this->db->select('sd.*, s.first_name, s.last_name, s.register_no, s.roll, c.name as class_name, se.name as section_name, dr.name as reason, b.school_name as branch_name');
        $this->db->from('student_dropout as sd');
        $this->db->join('student as s', 's.id = sd.student_id', 'left');
        $this->db->join('class as c', 'c.id = s.class_id', 'left');
        $this->db->join('section as se', 'se.id = s.section_id', 'left');
        $this->db->join('disable_reason as dr', 'dr.id = sd.reason_id', 'left');
        $this->db->join('branch as b', 'b.id = sd.branch_id', 'left');
        
        if (!is_superadmin_loggedin()) {
            $this->db->where('sd.branch_id', get_loggedin_branch_id());
        }
        
        return $this->db->get()->result_array();
    }

    public function save($data)
    {
        $branch_id = $this->application_model->get_branch_id();
        $arrayData = array(
            'student_id' => $data['student_id'],
            'branch_id' => $branch_id,
            'reason_id' => $data['reason_id'],
            'dropout_date' => date("Y-m-d", strtotime($data['dropout_date'])),
            'comments' => $data['comments'],
            'created_by' => get_loggedin_user_id(),
            'created_at' => date('Y-m-d H:i:s')
        );
        
        $this->db->insert('student_dropout', $arrayData);
        
        // Update student status to inactive
        $this->db->where('id', $data['student_id']);
        $this->db->update('student', array('active' => 0));
    }
}